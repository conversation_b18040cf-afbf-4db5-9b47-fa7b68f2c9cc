package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"vups_backend/internal/api/routes"
	"vups_backend/internal/config"
	"vups_backend/internal/services"
	"vups_backend/pkg/celery"
	"vups_backend/pkg/python"
	"vups_backend/pkg/redis"

	"github.com/sirupsen/logrus"

	_ "vups_backend/docs" // Import generated docs
)

// @title VUPS Backend API
// @version 1.0
// @description High-performance backend API framework for VUPS data processing and analysis
// @termsOfService http://swagger.io/terms/

// @contact.name API Support
// @contact.url http://www.swagger.io/support
// @contact.email <EMAIL>

// @license.name MIT
// @license.url https://opensource.org/licenses/MIT

// @host localhost:9022
// @BasePath /api/v1

// @securityDefinitions.basic BasicAuth

// @securityDefinitions.apikey ApiKeyAuth
// @in header
// @name Authorization

func main() {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Setup logger
	logger := setupLogger(cfg)
	logger.Info("Starting VUPS Backend API server...")

	// Initialize dependencies
	deps, err := initializeDependencies(cfg, logger)
	if err != nil {
		logger.Fatalf("Failed to initialize dependencies: %v", err)
	}
	defer deps.cleanup()

	// Setup routes
	router := routes.NewRouter(
		cfg,
		logger,
		deps.vupSearchService,
		deps.liveInfoService,
		deps.userDataService,
	)

	engine := router.SetupRoutes()

	// Create HTTP server
	server := &http.Server{
		Addr:         cfg.GetAddr(),
		Handler:      engine,
		ReadTimeout:  cfg.Server.ReadTimeout,
		WriteTimeout: cfg.Server.WriteTimeout,
		IdleTimeout:  cfg.Server.IdleTimeout,
	}

	// Start server in a goroutine
	go func() {
		logger.Infof("Server starting on %s", cfg.GetAddr())
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Fatalf("Failed to start server: %v", err)
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	logger.Info("Shutting down server...")

	// Graceful shutdown with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		logger.Errorf("Server forced to shutdown: %v", err)
	} else {
		logger.Info("Server exited gracefully")
	}
}

// Dependencies holds all the application dependencies
type Dependencies struct {
	redisClient      *redis.Client
	celeryClient     *celery.Client
	pythonExecutor   *python.Executor
	vupSearchService *services.VUPSearchService
	liveInfoService  *services.LiveInfoService
	userDataService  *services.UserDataService
}

// cleanup cleans up all dependencies
func (d *Dependencies) cleanup() {
	if d.redisClient != nil {
		d.redisClient.Close()
	}
	if d.pythonExecutor != nil {
		d.pythonExecutor.Close()
	}
}

// initializeDependencies initializes all application dependencies
func initializeDependencies(cfg *config.Config, logger *logrus.Logger) (*Dependencies, error) {
	deps := &Dependencies{}

	// Initialize Redis client
	logger.Info("Initializing Redis client...")
	redisClient, err := redis.NewClient(&cfg.Redis)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize Redis client: %w", err)
	}
	deps.redisClient = redisClient
	logger.Info("Redis client initialized successfully")

	// Initialize Celery client
	logger.Info("Initializing Celery client...")
	celeryClient := celery.NewClient(redisClient, &cfg.Celery)
	deps.celeryClient = celeryClient
	logger.Info("Celery client initialized successfully")

	// Initialize Python executor
	logger.Info("Initializing Python executor...")
	pythonExecutor := python.NewExecutor(&cfg.Python)
	deps.pythonExecutor = pythonExecutor
	logger.Info("Python executor initialized successfully")

	// Initialize services
	logger.Info("Initializing services...")

	deps.vupSearchService = services.NewVUPSearchService(
		pythonExecutor,
		redisClient,
		celeryClient,
		cfg,
	)

	deps.liveInfoService = services.NewLiveInfoService(
		pythonExecutor,
		redisClient,
		celeryClient,
		cfg,
	)

	deps.userDataService = services.NewUserDataService(
		pythonExecutor,
		redisClient,
		celeryClient,
		cfg,
	)

	logger.Info("All services initialized successfully")

	return deps, nil
}

// setupLogger configures and returns a logger instance
func setupLogger(cfg *config.Config) *logrus.Logger {
	logger := logrus.New()

	// Set log level
	level, err := logrus.ParseLevel(cfg.Logging.Level)
	if err != nil {
		level = logrus.InfoLevel
	}
	logger.SetLevel(level)

	// Set log format
	if cfg.Logging.Format == "json" {
		logger.SetFormatter(&logrus.JSONFormatter{
			TimestampFormat: time.RFC3339,
		})
	} else {
		logger.SetFormatter(&logrus.TextFormatter{
			FullTimestamp:   true,
			TimestampFormat: time.RFC3339,
		})
	}

	// Set output
	if cfg.Logging.Output == "file" && cfg.Logging.Filename != "" {
		file, err := os.OpenFile(cfg.Logging.Filename, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
		if err != nil {
			log.Printf("Failed to open log file, using stdout: %v", err)
		} else {
			logger.SetOutput(file)
		}
	}

	return logger
}

basePath: /api/v1
definitions:
  models.APIResponse:
    properties:
      code:
        type: integer
      data: {}
      details: {}
      message:
        type: string
    type: object
  models.ActivityData:
    properties:
      description:
        type: string
      timestamp:
        type: string
      type:
        type: string
      value:
        type: number
    type: object
  models.CommentData:
    properties:
      comment_id:
        type: string
      content:
        type: string
      like_count:
        type: integer
      publish_time:
        type: string
      reply_count:
        type: integer
      user_name:
        type: string
    type: object
  models.DanmuData:
    properties:
      datetime:
        type: string
      id:
        type: integer
      message:
        type: string
      room_id:
        type: string
      user_id:
        type: string
      user_name:
        type: string
    type: object
  models.DynamicData:
    properties:
      content:
        type: string
      dynamic_id:
        type: string
      like_count:
        type: integer
      publish_time:
        type: string
      share_count:
        type: integer
      type:
        type: integer
      view_count:
        type: integer
    type: object
  models.ErrorResponse:
    properties:
      code:
        type: integer
      details:
        type: string
      message:
        type: string
    type: object
  models.LiveAnalyticsData:
    properties:
      average_enter_room:
        type: integer
      average_online_rank:
        type: integer
      danmu_count:
        type: integer
      end_time:
        type: string
      enter_room_count:
        type: integer
      income:
        type: number
      interaction_count:
        type: integer
      live_id:
        type: string
      max_online_rank:
        type: integer
      pay_count:
        type: integer
      start_time:
        type: string
      watch_change_count:
        type: integer
    type: object
  models.LiveInfoRequest:
    properties:
      data_type:
        example: danmu
        type: string
      end_time:
        example: "2024-01-31"
        type: string
      room_id:
        example: "22886883"
        type: string
      start_time:
        example: "2024-01-01"
        type: string
    required:
    - room_id
    type: object
  models.LiveStatusData:
    properties:
      area:
        type: string
      cover:
        type: string
      datetime:
        type: string
      id:
        type: integer
      live_id:
        type: string
      parent_area:
        type: string
      room_id:
        type: string
      status:
        type: integer
      title:
        type: string
    type: object
  models.SuperChatData:
    properties:
      datetime:
        type: string
      id:
        type: integer
      message:
        type: string
      price:
        type: number
      room_id:
        type: string
      user_id:
        type: string
      user_name:
        type: string
    type: object
  models.TaskStatus:
    properties:
      created_at:
        type: string
      error:
        type: string
      result: {}
      status:
        type: string
      task_id:
        type: string
      updated_at:
        type: string
    type: object
  models.UserAnalyticsData:
    properties:
      dahanghai_growth_rate:
        type: number
      follower_growth_rate:
        type: number
      recent_activity:
        items:
          $ref: '#/definitions/models.ActivityData'
        type: array
      top_comments:
        items:
          $ref: '#/definitions/models.CommentData'
        type: array
      top_videos:
        items:
          $ref: '#/definitions/models.VideoData'
        type: array
      uid:
        type: string
    type: object
  models.UserContentData:
    properties:
      dynamics:
        items:
          $ref: '#/definitions/models.DynamicData'
        type: array
      videos:
        items:
          $ref: '#/definitions/models.VideoData'
        type: array
    type: object
  models.UserDataRequest:
    properties:
      data_type:
        example: stats
        type: string
      end_time:
        example: "2024-01-31"
        type: string
      start_time:
        example: "2024-01-01"
        type: string
      uid:
        example: "401315430"
        type: string
    required:
    - uid
    type: object
  models.UserInfoData:
    properties:
      avatar:
        type: string
      description:
        type: string
      join_time:
        type: string
      level:
        type: integer
      name:
        type: string
      stats:
        $ref: '#/definitions/models.UserStatsData'
      uid:
        type: string
      vip_status:
        type: integer
    type: object
  models.UserStatsData:
    properties:
      dahanghai_count:
        type: integer
      dynamic_count:
        type: integer
      follower_count:
        type: integer
      last_updated:
        type: string
      like_count:
        type: integer
      uid:
        type: string
      video_count:
        type: integer
      view_count:
        type: integer
    type: object
  models.VUPSearchRequest:
    properties:
      question:
        example: 查询星瞳的最新数据
        type: string
      stream:
        example: false
        type: boolean
    required:
    - question
    type: object
  models.VUPSearchResponse:
    properties:
      answer:
        example: 根据最新数据，星瞳的粉丝数为...
        type: string
      task_id:
        example: task_123456
        type: string
    type: object
  models.VideoData:
    properties:
      bvid:
        type: string
      coin_count:
        type: integer
      description:
        type: string
      duration:
        type: integer
      like_count:
        type: integer
      publish_time:
        type: string
      share_count:
        type: integer
      title:
        type: string
      view_count:
        type: integer
    type: object
host: localhost:9022
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: http://www.swagger.io/support
  description: High-performance backend API framework for VUPS data processing and
    analysis
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT
  termsOfService: http://swagger.io/terms/
  title: VUPS Backend API
  version: "1.0"
paths:
  /api/v1/live/analytics:
    get:
      description: Query live analytics data by room ID
      parameters:
      - description: Room ID
        in: query
        name: room_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/models.APIResponse'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/models.LiveAnalyticsData'
                  type: array
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: Query live analytics
      tags:
      - Live Info
  /api/v1/live/async:
    post:
      consumes:
      - application/json
      description: Perform an async live info query using Celery
      parameters:
      - description: Live info request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.LiveInfoRequest'
      produces:
      - application/json
      responses:
        "202":
          description: Accepted
          schema:
            allOf:
            - $ref: '#/definitions/models.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/models.TaskStatus'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: Async live info query
      tags:
      - Live Info
  /api/v1/live/current:
    get:
      description: Query current live information by room ID
      parameters:
      - description: Room ID
        in: query
        name: room_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.APIResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: Query current live info
      tags:
      - Live Info
  /api/v1/live/danmu:
    get:
      description: Query danmu data by room ID and time range
      parameters:
      - description: Room ID
        in: query
        name: room_id
        required: true
        type: string
      - description: Start time (YYYY-MM-DD or timestamp)
        in: query
        name: start_time
        required: true
        type: string
      - description: End time (YYYY-MM-DD or timestamp)
        in: query
        name: end_time
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/models.APIResponse'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/models.DanmuData'
                  type: array
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: Query danmu data
      tags:
      - Live Info
  /api/v1/live/status:
    get:
      description: Query live status by room ID and time
      parameters:
      - description: Room ID
        in: query
        name: room_id
        required: true
        type: string
      - description: Timestamp
        in: query
        name: start_time
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/models.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/models.LiveStatusData'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: Query live status
      tags:
      - Live Info
  /api/v1/live/superchat:
    get:
      description: Query superchat data by room ID and time range
      parameters:
      - description: Room ID
        in: query
        name: room_id
        required: true
        type: string
      - description: Start time (YYYY-MM-DD or timestamp)
        in: query
        name: start_time
        required: true
        type: string
      - description: End time (YYYY-MM-DD or timestamp)
        in: query
        name: end_time
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/models.APIResponse'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/models.SuperChatData'
                  type: array
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: Query superchat data
      tags:
      - Live Info
  /api/v1/user/{uid}/analytics:
    get:
      description: Get user analytics data including top comments and videos by UID
      parameters:
      - description: User UID
        in: path
        name: uid
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/models.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/models.UserAnalyticsData'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: Get user analytics
      tags:
      - User Data
  /api/v1/user/{uid}/content:
    get:
      description: Get user content including videos and dynamics by UID
      parameters:
      - description: User UID
        in: path
        name: uid
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/models.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/models.UserContentData'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: Get user content
      tags:
      - User Data
  /api/v1/user/{uid}/info:
    get:
      description: Get comprehensive user information by UID
      parameters:
      - description: User UID
        in: path
        name: uid
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/models.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/models.UserInfoData'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: Get user info
      tags:
      - User Data
  /api/v1/user/{uid}/period-stats:
    get:
      description: Get user statistics for a specific time period by UID
      parameters:
      - description: User UID
        in: path
        name: uid
        required: true
        type: string
      - description: Start time (YYYY-MM-DD)
        in: query
        name: start_time
        required: true
        type: string
      - description: End time (YYYY-MM-DD)
        in: query
        name: end_time
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.APIResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: Get user period statistics
      tags:
      - User Data
  /api/v1/user/{uid}/stats:
    get:
      description: Get user statistics by UID
      parameters:
      - description: User UID
        in: path
        name: uid
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/models.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/models.UserStatsData'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: Get user statistics
      tags:
      - User Data
  /api/v1/user/async:
    post:
      consumes:
      - application/json
      description: Perform an async user data query using Celery
      parameters:
      - description: User data request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.UserDataRequest'
      produces:
      - application/json
      responses:
        "202":
          description: Accepted
          schema:
            allOf:
            - $ref: '#/definitions/models.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/models.TaskStatus'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: Async user data query
      tags:
      - User Data
  /api/v1/vup/search:
    post:
      consumes:
      - application/json
      description: Perform a VUP search query
      parameters:
      - description: Search request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.VUPSearchRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/models.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/models.VUPSearchResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: Search VUP data
      tags:
      - VUP Search
  /api/v1/vup/search/async:
    post:
      consumes:
      - application/json
      description: Perform an async VUP search query using Celery
      parameters:
      - description: Search request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.VUPSearchRequest'
      produces:
      - application/json
      responses:
        "202":
          description: Accepted
          schema:
            allOf:
            - $ref: '#/definitions/models.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/models.VUPSearchResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: Async VUP search
      tags:
      - VUP Search
  /api/v1/vup/search/stream:
    post:
      consumes:
      - application/json
      description: Perform a VUP search query with streaming response
      parameters:
      - description: Search request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.VUPSearchRequest'
      produces:
      - text/event-stream
      responses:
        "200":
          description: Server-Sent Events stream
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: Stream VUP search
      tags:
      - VUP Search
  /api/v1/vup/search/task/{task_id}:
    get:
      description: Get the result of an async VUP search task
      parameters:
      - description: Task ID
        in: path
        name: task_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/models.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/models.TaskStatus'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: Get task result
      tags:
      - VUP Search
securityDefinitions:
  ApiKeyAuth:
    in: header
    name: Authorization
    type: apiKey
  BasicAuth:
    type: basic
swagger: "2.0"

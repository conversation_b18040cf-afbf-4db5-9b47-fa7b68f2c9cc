// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "termsOfService": "http://swagger.io/terms/",
        "contact": {
            "name": "API Support",
            "url": "http://www.swagger.io/support",
            "email": "<EMAIL>"
        },
        "license": {
            "name": "MIT",
            "url": "https://opensource.org/licenses/MIT"
        },
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/api/v1/live/analytics": {
            "get": {
                "description": "Query live analytics data by room ID",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Live Info"
                ],
                "summary": "Query live analytics",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Room ID",
                        "name": "room_id",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.APIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/models.LiveAnalyticsData"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/live/async": {
            "post": {
                "description": "Perform an async live info query using Celery",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Live Info"
                ],
                "summary": "Async live info query",
                "parameters": [
                    {
                        "description": "Live info request",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.LiveInfoRequest"
                        }
                    }
                ],
                "responses": {
                    "202": {
                        "description": "Accepted",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.APIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.TaskStatus"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/live/current": {
            "get": {
                "description": "Query current live information by room ID",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Live Info"
                ],
                "summary": "Query current live info",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Room ID",
                        "name": "room_id",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.APIResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/live/danmu": {
            "get": {
                "description": "Query danmu data by room ID and time range",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Live Info"
                ],
                "summary": "Query danmu data",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Room ID",
                        "name": "room_id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Start time (YYYY-MM-DD or timestamp)",
                        "name": "start_time",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "End time (YYYY-MM-DD or timestamp)",
                        "name": "end_time",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.APIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/models.DanmuData"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/live/status": {
            "get": {
                "description": "Query live status by room ID and time",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Live Info"
                ],
                "summary": "Query live status",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Room ID",
                        "name": "room_id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Timestamp",
                        "name": "start_time",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.APIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.LiveStatusData"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/live/superchat": {
            "get": {
                "description": "Query superchat data by room ID and time range",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Live Info"
                ],
                "summary": "Query superchat data",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Room ID",
                        "name": "room_id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Start time (YYYY-MM-DD or timestamp)",
                        "name": "start_time",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "End time (YYYY-MM-DD or timestamp)",
                        "name": "end_time",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.APIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/models.SuperChatData"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/user/async": {
            "post": {
                "description": "Perform an async user data query using Celery",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User Data"
                ],
                "summary": "Async user data query",
                "parameters": [
                    {
                        "description": "User data request",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.UserDataRequest"
                        }
                    }
                ],
                "responses": {
                    "202": {
                        "description": "Accepted",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.APIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.TaskStatus"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/user/{uid}/analytics": {
            "get": {
                "description": "Get user analytics data including top comments and videos by UID",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User Data"
                ],
                "summary": "Get user analytics",
                "parameters": [
                    {
                        "type": "string",
                        "description": "User UID",
                        "name": "uid",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.APIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.UserAnalyticsData"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/user/{uid}/content": {
            "get": {
                "description": "Get user content including videos and dynamics by UID",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User Data"
                ],
                "summary": "Get user content",
                "parameters": [
                    {
                        "type": "string",
                        "description": "User UID",
                        "name": "uid",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.APIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.UserContentData"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/user/{uid}/info": {
            "get": {
                "description": "Get comprehensive user information by UID",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User Data"
                ],
                "summary": "Get user info",
                "parameters": [
                    {
                        "type": "string",
                        "description": "User UID",
                        "name": "uid",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.APIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.UserInfoData"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/user/{uid}/period-stats": {
            "get": {
                "description": "Get user statistics for a specific time period by UID",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User Data"
                ],
                "summary": "Get user period statistics",
                "parameters": [
                    {
                        "type": "string",
                        "description": "User UID",
                        "name": "uid",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Start time (YYYY-MM-DD)",
                        "name": "start_time",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "End time (YYYY-MM-DD)",
                        "name": "end_time",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.APIResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/user/{uid}/stats": {
            "get": {
                "description": "Get user statistics by UID",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User Data"
                ],
                "summary": "Get user statistics",
                "parameters": [
                    {
                        "type": "string",
                        "description": "User UID",
                        "name": "uid",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.APIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.UserStatsData"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/vup/search": {
            "post": {
                "description": "Perform a VUP search query",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "VUP Search"
                ],
                "summary": "Search VUP data",
                "parameters": [
                    {
                        "description": "Search request",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.VUPSearchRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.APIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.VUPSearchResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/vup/search/async": {
            "post": {
                "description": "Perform an async VUP search query using Celery",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "VUP Search"
                ],
                "summary": "Async VUP search",
                "parameters": [
                    {
                        "description": "Search request",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.VUPSearchRequest"
                        }
                    }
                ],
                "responses": {
                    "202": {
                        "description": "Accepted",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.APIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.VUPSearchResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/vup/search/stream": {
            "post": {
                "description": "Perform a VUP search query with streaming response",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "text/event-stream"
                ],
                "tags": [
                    "VUP Search"
                ],
                "summary": "Stream VUP search",
                "parameters": [
                    {
                        "description": "Search request",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.VUPSearchRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Server-Sent Events stream",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/vup/search/task/{task_id}": {
            "get": {
                "description": "Get the result of an async VUP search task",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "VUP Search"
                ],
                "summary": "Get task result",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Task ID",
                        "name": "task_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.APIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.TaskStatus"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "models.APIResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {},
                "details": {},
                "message": {
                    "type": "string"
                }
            }
        },
        "models.ActivityData": {
            "type": "object",
            "properties": {
                "description": {
                    "type": "string"
                },
                "timestamp": {
                    "type": "string"
                },
                "type": {
                    "type": "string"
                },
                "value": {
                    "type": "number"
                }
            }
        },
        "models.CommentData": {
            "type": "object",
            "properties": {
                "comment_id": {
                    "type": "string"
                },
                "content": {
                    "type": "string"
                },
                "like_count": {
                    "type": "integer"
                },
                "publish_time": {
                    "type": "string"
                },
                "reply_count": {
                    "type": "integer"
                },
                "user_name": {
                    "type": "string"
                }
            }
        },
        "models.DanmuData": {
            "type": "object",
            "properties": {
                "datetime": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "message": {
                    "type": "string"
                },
                "room_id": {
                    "type": "string"
                },
                "user_id": {
                    "type": "string"
                },
                "user_name": {
                    "type": "string"
                }
            }
        },
        "models.DynamicData": {
            "type": "object",
            "properties": {
                "content": {
                    "type": "string"
                },
                "dynamic_id": {
                    "type": "string"
                },
                "like_count": {
                    "type": "integer"
                },
                "publish_time": {
                    "type": "string"
                },
                "share_count": {
                    "type": "integer"
                },
                "type": {
                    "type": "integer"
                },
                "view_count": {
                    "type": "integer"
                }
            }
        },
        "models.ErrorResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "details": {
                    "type": "string"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "models.LiveAnalyticsData": {
            "type": "object",
            "properties": {
                "average_enter_room": {
                    "type": "integer"
                },
                "average_online_rank": {
                    "type": "integer"
                },
                "danmu_count": {
                    "type": "integer"
                },
                "end_time": {
                    "type": "string"
                },
                "enter_room_count": {
                    "type": "integer"
                },
                "income": {
                    "type": "number"
                },
                "interaction_count": {
                    "type": "integer"
                },
                "live_id": {
                    "type": "string"
                },
                "max_online_rank": {
                    "type": "integer"
                },
                "pay_count": {
                    "type": "integer"
                },
                "start_time": {
                    "type": "string"
                },
                "watch_change_count": {
                    "type": "integer"
                }
            }
        },
        "models.LiveInfoRequest": {
            "type": "object",
            "required": [
                "room_id"
            ],
            "properties": {
                "data_type": {
                    "type": "string",
                    "example": "danmu"
                },
                "end_time": {
                    "type": "string",
                    "example": "2024-01-31"
                },
                "room_id": {
                    "type": "string",
                    "example": "22886883"
                },
                "start_time": {
                    "type": "string",
                    "example": "2024-01-01"
                }
            }
        },
        "models.LiveStatusData": {
            "type": "object",
            "properties": {
                "area": {
                    "type": "string"
                },
                "cover": {
                    "type": "string"
                },
                "datetime": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "live_id": {
                    "type": "string"
                },
                "parent_area": {
                    "type": "string"
                },
                "room_id": {
                    "type": "string"
                },
                "status": {
                    "type": "integer"
                },
                "title": {
                    "type": "string"
                }
            }
        },
        "models.SuperChatData": {
            "type": "object",
            "properties": {
                "datetime": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "message": {
                    "type": "string"
                },
                "price": {
                    "type": "number"
                },
                "room_id": {
                    "type": "string"
                },
                "user_id": {
                    "type": "string"
                },
                "user_name": {
                    "type": "string"
                }
            }
        },
        "models.TaskStatus": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "error": {
                    "type": "string"
                },
                "result": {},
                "status": {
                    "type": "string"
                },
                "task_id": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "models.UserAnalyticsData": {
            "type": "object",
            "properties": {
                "dahanghai_growth_rate": {
                    "type": "number"
                },
                "follower_growth_rate": {
                    "type": "number"
                },
                "recent_activity": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.ActivityData"
                    }
                },
                "top_comments": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.CommentData"
                    }
                },
                "top_videos": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.VideoData"
                    }
                },
                "uid": {
                    "type": "string"
                }
            }
        },
        "models.UserContentData": {
            "type": "object",
            "properties": {
                "dynamics": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.DynamicData"
                    }
                },
                "videos": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.VideoData"
                    }
                }
            }
        },
        "models.UserDataRequest": {
            "type": "object",
            "required": [
                "uid"
            ],
            "properties": {
                "data_type": {
                    "type": "string",
                    "example": "stats"
                },
                "end_time": {
                    "type": "string",
                    "example": "2024-01-31"
                },
                "start_time": {
                    "type": "string",
                    "example": "2024-01-01"
                },
                "uid": {
                    "type": "string",
                    "example": "401315430"
                }
            }
        },
        "models.UserInfoData": {
            "type": "object",
            "properties": {
                "avatar": {
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "join_time": {
                    "type": "string"
                },
                "level": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "stats": {
                    "$ref": "#/definitions/models.UserStatsData"
                },
                "uid": {
                    "type": "string"
                },
                "vip_status": {
                    "type": "integer"
                }
            }
        },
        "models.UserStatsData": {
            "type": "object",
            "properties": {
                "dahanghai_count": {
                    "type": "integer"
                },
                "dynamic_count": {
                    "type": "integer"
                },
                "follower_count": {
                    "type": "integer"
                },
                "last_updated": {
                    "type": "string"
                },
                "like_count": {
                    "type": "integer"
                },
                "uid": {
                    "type": "string"
                },
                "video_count": {
                    "type": "integer"
                },
                "view_count": {
                    "type": "integer"
                }
            }
        },
        "models.VUPSearchRequest": {
            "type": "object",
            "required": [
                "question"
            ],
            "properties": {
                "question": {
                    "type": "string",
                    "example": "查询星瞳的最新数据"
                },
                "stream": {
                    "type": "boolean",
                    "example": false
                }
            }
        },
        "models.VUPSearchResponse": {
            "type": "object",
            "properties": {
                "answer": {
                    "type": "string",
                    "example": "根据最新数据，星瞳的粉丝数为..."
                },
                "task_id": {
                    "type": "string",
                    "example": "task_123456"
                }
            }
        },
        "models.VideoData": {
            "type": "object",
            "properties": {
                "bvid": {
                    "type": "string"
                },
                "coin_count": {
                    "type": "integer"
                },
                "description": {
                    "type": "string"
                },
                "duration": {
                    "type": "integer"
                },
                "like_count": {
                    "type": "integer"
                },
                "publish_time": {
                    "type": "string"
                },
                "share_count": {
                    "type": "integer"
                },
                "title": {
                    "type": "string"
                },
                "view_count": {
                    "type": "integer"
                }
            }
        }
    },
    "securityDefinitions": {
        "ApiKeyAuth": {
            "type": "apiKey",
            "name": "Authorization",
            "in": "header"
        },
        "BasicAuth": {
            "type": "basic"
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "1.0",
	Host:             "localhost:9022",
	BasePath:         "/api/v1",
	Schemes:          []string{},
	Title:            "VUPS Backend API",
	Description:      "High-performance backend API framework for VUPS data processing and analysis",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
